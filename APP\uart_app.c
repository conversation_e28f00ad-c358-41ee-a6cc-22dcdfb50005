#include "uart_app.h"
/*����1*/
extern uint8_t uart_rx_dma_buffer[BUFFER_SIZE]; // DMA ��ȡ������
extern uint8_t ring_buffer_input[BUFFER_SIZE]; // ���λ�������Ӧ����������
extern struct rt_ringbuffer ring_buffer; // ���λ�����
extern uint8_t uart_data_buffer[BUFFER_SIZE]; // ���ݴ��������� 
/*����2*/
extern uint8_t uart2_rx_dma_buffer[BUFFER_SIZE]; // DMA ��ȡ������
extern uint8_t ring_buffer_input2[BUFFER_SIZE]; // ���λ�������Ӧ����������
extern struct rt_ringbuffer ring_buffer2; // ���λ�����
extern uint8_t uart2_data_buffer[BUFFER_SIZE]; // ���ݴ���������
extern DMA_HandleTypeDef hdma_usart2_rx;
/*����4*/
extern uint8_t uart4_rx_dma_buffer[BUFFER_SIZE]; // DMA ��ȡ������
extern uint8_t ring_buffer_input4[BUFFER_SIZE]; // ���λ�������Ӧ����������
extern struct rt_ringbuffer ring_buffer4; // ���λ�����
extern uint8_t uart4_data_buffer[BUFFER_SIZE]; // ���ݴ���������
extern DMA_HandleTypeDef hdma_uart4_rx;
/*����5*/
extern uint8_t uart5_rx_dma_buffer[BUFFER_SIZE]; // DMA ��ȡ������
extern uint8_t ring_buffer_input5[BUFFER_SIZE]; // ���λ�������Ӧ����������
extern struct rt_ringbuffer ring_buffer5; // ���λ�����
extern uint8_t uart5_data_buffer[BUFFER_SIZE]; // ���ݴ���������
extern DMA_HandleTypeDef hdma_uart5_rx;
/*����6*/
extern uint8_t uart6_rx_dma_buffer[BUFFER_SIZE]; // DMA ��ȡ������
extern uint8_t ring_buffer_input6[BUFFER_SIZE]; // ���λ�������Ӧ����������
extern struct rt_ringbuffer ring_buffer6; // ���λ�����
extern uint8_t uart6_data_buffer[BUFFER_SIZE]; // ���ݴ���������
extern DMA_HandleTypeDef hdma_usart6_rx;



void Uart_Init(void)
{
	/*����1*/
  rt_ringbuffer_init(&ring_buffer, ring_buffer_input, BUFFER_SIZE);
  HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, BUFFER_SIZE); // ������ȡ�ж�
  __HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT); // �ر� DMA ��"�����ж�"����
	/*����2*/
	 rt_ringbuffer_init(&ring_buffer2, ring_buffer_input2, BUFFER_SIZE);
  HAL_UARTEx_ReceiveToIdle_DMA(&huart2, uart2_rx_dma_buffer, BUFFER_SIZE); // ������ȡ�ж�
  __HAL_DMA_DISABLE_IT(&hdma_usart2_rx, DMA_IT_HT); // �ر� DMA ��"�����ж�"����
	/*����4*/
	rt_ringbuffer_init(&ring_buffer4, ring_buffer_input4, BUFFER_SIZE);
  HAL_UARTEx_ReceiveToIdle_DMA(&huart4, uart4_rx_dma_buffer, BUFFER_SIZE); // ������ȡ�ж�
  __HAL_DMA_DISABLE_IT(&hdma_uart4_rx, DMA_IT_HT); // �ر� DMA ��"�����ж�"����
	/*����5*/
	rt_ringbuffer_init(&ring_buffer5, ring_buffer_input5, BUFFER_SIZE);
  HAL_UARTEx_ReceiveToIdle_DMA(&huart5, uart5_rx_dma_buffer, BUFFER_SIZE); // ������ȡ�ж�
  __HAL_DMA_DISABLE_IT(&hdma_uart5_rx, DMA_IT_HT); // �ر� DMA ��"�����ж�"����	
	/*����6*/
	rt_ringbuffer_init(&ring_buffer6, ring_buffer_input6, BUFFER_SIZE);
  HAL_UARTEx_ReceiveToIdle_DMA(&huart6, uart6_rx_dma_buffer, BUFFER_SIZE); // ������ȡ�ж�
  __HAL_DMA_DISABLE_IT(&hdma_usart6_rx, DMA_IT_HT); // �ر� DMA ��"�����ж�"����
}

void Uart_Task(void)
{
  uint16_t uart_data_len  = rt_ringbuffer_data_len(&ring_buffer);
	uint16_t uart2_data_len = rt_ringbuffer_data_len(&ring_buffer2);
	uint16_t uart4_data_len = rt_ringbuffer_data_len(&ring_buffer4);
	uint16_t uart5_data_len = rt_ringbuffer_data_len(&ring_buffer5);
	uint16_t uart6_data_len = rt_ringbuffer_data_len(&ring_buffer6);
	
  if(uart_data_len > 0)
  {
    rt_ringbuffer_get(&ring_buffer, uart_data_buffer, uart_data_len);
    uart_data_buffer[uart_data_len] = '\0';
    /* ���ݽ��� */
    my_printf(&huart1, "Ringbuffer 1:%s\r\n", uart_data_buffer);
    memset(uart_data_buffer, 0, uart_data_len);
  }
	 if(uart2_data_len>0)
	{
		rt_ringbuffer_get(&ring_buffer2, uart2_data_buffer, uart2_data_len);
    uart2_data_buffer[uart2_data_len] = '\0';

    /* X轴步进电机编码器数据解析 */
    for(uint16_t i = 0; i < uart2_data_len; i++)
    {
        Motor_X_Receive_Data(uart2_data_buffer[i]);
    }

    /* ���ݽ��� */
    my_printf(&huart1, "Ringbuffer 2:%s\r\n", uart2_data_buffer);
    memset(uart2_data_buffer, 0, uart2_data_len);

	}
	 if(uart4_data_len>0)
	{
		rt_ringbuffer_get(&ring_buffer4, uart4_data_buffer, uart4_data_len);
    uart4_data_buffer[uart4_data_len] = '\0';

    /* Y轴步进电机编码器数据解析 */
    for(uint16_t i = 0; i < uart4_data_len; i++)
    {
        Motor_Y_Receive_Data(uart4_data_buffer[i]);
    }

    /* ���ݽ��� */
    my_printf(&huart1, "Ringbuffer 4:%s\r\n", uart4_data_buffer);
    memset(uart4_data_buffer, 0, uart4_data_len);
	}
	if(uart5_data_len>0)
	{
		rt_ringbuffer_get(&ring_buffer5, uart5_data_buffer, uart5_data_len);
    uart5_data_buffer[uart5_data_len] = '\0';
		HWT101_ProcessBuffer(&hwt101,uart5_data_buffer,uart5_data_len);
		float yaw=HWT101_GetYaw(&hwt101);
		float gyro_z=HWT101_GetGyroZ(&hwt101);
		
		HWT101_Data_t* data =HWT101_GetData(&hwt101);
		if(data !=NULL)
		{
			/* ���ݽ��� */
			my_printf(&huart1, "Yaw=%.2f,GyroZ:%.2f/s\r\n", data->yaw,data->gyro_z);
		}
    memset(uart5_data_buffer, 0, uart5_data_len);
	}
	if(uart6_data_len>0)
	{
			rt_ringbuffer_get(&ring_buffer6, uart6_data_buffer, uart6_data_len);
			uart6_data_buffer[uart6_data_len] = '\0';
		 
			my_printf(&huart1, "Ringbuffer 6:%s\r\n", uart6_data_buffer);
			memset(uart6_data_buffer, 0, uart6_data_len);
	}


}

