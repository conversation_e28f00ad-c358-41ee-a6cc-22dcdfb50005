/**
 * @file app_motor.h
 * @brief ����EmmV5�ĵ�����ƺ���
 * @copyright �״׵��ӹ�����
 */

#ifndef __STEPMOTOR_APP_H_
#define __STEPMOTOR_APP_H_

#include "MyDefine.h"

/* ������ƺ궨�� */
#define MOTOR_X_ADDR        0x01          // X������ַ
#define MOTOR_Y_ADDR        0x01          // Y������ַ
#define MOTOR_X_UART        huart2        // X�������� ��
#define MOTOR_Y_UART        huart4        // Y�������� ��
#define MOTOR_MAX_SPEED     3            // ������ת��(RPM)
#define MOTOR_ACCEL         0             // ������ٶ�(0��ʾֱ������)
#define MOTOR_SYNC_FLAG     false         // ���ͬ����־
#define MOTOR_MAX_ANGLE     50            // ������Ƕ�����(��50��)

/* 步进电机编码器通信协议定义 */
#define MOTOR_START_BYTE_01     0x01    // 数据包起始字节
#define MOTOR_END_BYTE          0x6B    // 数据包结束字节
#define MOTOR_READY_FLAG_1      0xFD    // 电机就绪标志1
#define MOTOR_READY_FLAG_2      0x9F    // 电机就绪标志2
#define MOTOR_RX_BUFFER_SIZE    10      // 接收缓冲区大小

/* 调试输出宏定义 - 使用您的my_printf函数 */
#define DEBUG_PRINTF(huart, format, ...) my_printf(huart, format, ##__VA_ARGS__)

/* �켣��ֵ���ض��� */
#define TRAJECTORY_BUFFER_SIZE  100          // �켣������С
#define TRAJECTORY_STEP_TIME    20           // �켣ִ�в���ʱ��(ms)

/* �켣�㽹���� */
typedef struct {
    int16_t x;                               // X����λ��
    int16_t y;                               // Y����λ��
    uint16_t speed;                          // ����ĵ��ٶȰ٧ֱ�
} TrajectoryPoint_t;

/* �켣���������� */
typedef struct {
    TrajectoryPoint_t points[TRAJECTORY_BUFFER_SIZE];  // �켣������
    uint16_t head;                           // ����ͷָ��
    uint16_t tail;                           // ����βָ��
    uint16_t count;                          // ��ǰ����
    bool is_executing;                       // �Ƿ���ִ���켣
} TrajectoryBuffer_t;

/* �������� */
void StepMotor_Init(void);                    // �����ʼ��
void StepMotor_Set_Speed(int8_t x_percent, int8_t y_percent);  // ����XY����ٶ�(�ٷֱ�)
void StepMotor_Stop(void);                    // ֹͣ���е��

/* �켣��ֵ���� */
void StepMotor_Trajectory_Init(void);         // ��ʼ���켣ϵͳ
bool StepMotor_Move_To_Point(int16_t target_x, int16_t target_y, uint16_t speed_percent);  // �ƶ�����ָ���
void StepMotor_Trajectory_Execute(void);      // �켣ִ�к���(����ʱ����е���)
bool StepMotor_Is_Trajectory_Complete(void);  // ���켣�Ƿ�ִ�����
void StepMotor_Clear_Trajectory(void);        // ��ճ켣������
void StepMotor_Move_Pulses(int32_t x_pulses, int32_t y_pulses);

/* 编码器数据接收函数声明 */
void Motor_X_Receive_Data(uint8_t com_data);
void Motor_Y_Receive_Data(uint8_t com_data);

/* 编码器接收相关全局变量声明 */
extern uint8_t motor_x_rx_state;                    // X轴接收状态机状态
extern uint8_t motor_x_rx_buffer[MOTOR_RX_BUFFER_SIZE]; // X轴接收缓冲区
extern uint8_t motor_x_rx_counter;                  // X轴接收计数器
extern uint8_t motor_y_rx_state;                    // Y轴接收状态机状态
extern uint8_t motor_y_rx_buffer[MOTOR_RX_BUFFER_SIZE]; // Y轴接收缓冲区
extern uint8_t motor_y_rx_counter;                  // Y轴接收计数器

/* 电机状态标志声明 */
extern uint8_t motor01_ready;                       // X轴电机就绪标志
extern uint8_t motor02_ready;                       // Y轴电机就绪标志
extern uint8_t stop_flag_car;                       // 小车停止标志

/* 电机脉冲计数相关变量声明 */
extern int32_t motor_x_position;                    // X轴当前位置(脉冲数)
extern int32_t motor_y_position;                    // Y轴当前位置(脉冲数)
extern int32_t motor_x_encoder;                     // X轴编码器值
extern int32_t motor_y_encoder;                     // Y轴编码器值
extern uint8_t motor_x_direction;                   // X轴方向(0=CW, 1=CCW)
extern uint8_t motor_y_direction;                   // Y轴方向(0=CW, 1=CCW)
extern int16_t motor_x_speed;                       // X轴实时速度(RPM)
extern int16_t motor_y_speed;                       // Y轴实时速度(RPM)

/* 串口接收字节变量声明 */
extern uint8_t motor_x_rx_byte;                     // X轴串口接收字节
extern uint8_t motor_y_rx_byte;                     // Y轴串口接收字节

/* 编码器系统初始化和控制函数 */
void StepMotor_Encoder_Init(void);                  // 编码器系统初始化
void StepMotor_Start_Receive(void);                 // 启动串口接收
void StepMotor_Check_Status(void);                  // 检查电机状态

/* 脉冲计数查询函数 */
int32_t StepMotor_Get_X_Position(void);             // 获取X轴位置(脉冲数)
int32_t StepMotor_Get_Y_Position(void);             // 获取Y轴位置(脉冲数)
int32_t StepMotor_Get_X_Encoder(void);              // 获取X轴编码器值
int32_t StepMotor_Get_Y_Encoder(void);              // 获取Y轴编码器值
void StepMotor_Reset_Position(void);                // 重置位置计数器
void StepMotor_Print_Status(void);                  // 打印电机状态信息
void StepMotor_Request_Position(void);              // 主动请求位置信息
void StepMotor_Request_Encoder(void);               // 主动请求编码器信息

#endif /* __APP_MOTOR_H_ */
