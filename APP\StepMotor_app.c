#include "MyDefine.h"
#include "StepMotor_app.h"
#include <math.h>
#include <stdlib.h>

/* 串口接收字节变量定义 */
uint8_t motor_x_rx_byte = 0;
uint8_t motor_y_rx_byte = 0;

/* 编码器接收相关变量定义 */
uint8_t motor_x_rx_state = 0;                       // X轴接收状态机状态
uint8_t motor_x_rx_buffer[MOTOR_RX_BUFFER_SIZE];     // X轴接收缓冲区
uint8_t motor_x_rx_counter = 0;                     // X轴接收计数器
uint8_t motor_y_rx_state = 0;                       // Y轴接收状态机状态
uint8_t motor_y_rx_buffer[MOTOR_RX_BUFFER_SIZE];     // Y轴接收缓冲区
uint8_t motor_y_rx_counter = 0;                     // Y轴接收计数器

/* 电机状态标志定义 */
uint8_t motor01_ready = 0;                          // X轴电机就绪标志
uint8_t motor02_ready = 0;                          // Y轴电机就绪标志
uint8_t stop_flag_car = 0;                          // 小车停止标志

/* 电机脉冲计数相关变量定义 */
int32_t motor_x_position = 0;                       // X轴当前位置(脉冲数)
int32_t motor_y_position = 0;                       // Y轴当前位置(脉冲数)
int32_t motor_x_encoder = 0;                        // X轴编码器值
int32_t motor_y_encoder = 0;                        // Y轴编码器值
uint8_t motor_x_direction = 0;                      // X轴方向(0=CW, 1=CCW)
uint8_t motor_y_direction = 0;                      // Y轴方向(0=CW, 1=CCW)
int16_t motor_x_speed = 0;                          // X轴实时速度(RPM)
int16_t motor_y_speed = 0;                          // Y轴实时速度(RPM)
/**
 * @brief �����ʼ������
 */
void StepMotor_Init(void)
{
    /* ʹ��X���� */
    Emm_V5_En_Control(&MOTOR_X_UART, MOTOR_X_ADDR, true, MOTOR_SYNC_FLAG);

    /* ʹ��Y���� */
    Emm_V5_En_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, true, MOTOR_SYNC_FLAG);

    /* ��ʼֹͣ */
    StepMotor_Stop();
}

/**
 * @brief ����XY�����ٶ�
 * @param x_percent X���ٶȰٷֱȣ���Χ-100��100
 * @param y_percent Y���ٶȰٷֱȣ���Χ-100��100
 */
void StepMotor_Set_Speed(int8_t x_percent, int8_t y_percent)
{
    uint8_t x_dir, y_dir;
    uint16_t x_speed, y_speed;

    /* ���ưٷֱȷ�Χ */
    if (x_percent > 100)
        x_percent = 100;
    if (x_percent < -100)
        x_percent = -100;
    if (y_percent > 100)
        y_percent = 100;
    if (y_percent < -100)
        y_percent = -100;

    /* ����X�᷽�� */
    if (x_percent >= 0)
    {
        x_dir = 0; /* CW���� */
    }
    else
    {
        x_dir = 1;              /* CCW���� */
        x_percent = -x_percent; /* ȡ����ֵ */
    }

    /* ����Y�᷽�� */
    if (y_percent >= 0)
    {
        y_dir = 0; /* CW���� */
    }
    else
    {
        y_dir = 1;              /* CCW���� */
        y_percent = -y_percent; /* ȡ����ֵ */
    }

    /* ����ʵ���ٶ�ֵ(�ٷֱ�ת��ΪRPM) */
    x_speed = (uint16_t)((x_percent * MOTOR_MAX_SPEED) / 100);
    y_speed = (uint16_t)((y_percent * MOTOR_MAX_SPEED) / 100);

    /* ����X���� */
    Emm_V5_Vel_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, x_speed, MOTOR_ACCEL, MOTOR_SYNC_FLAG);

    /* ����Y���� */
    Emm_V5_Vel_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, y_speed, MOTOR_ACCEL, MOTOR_SYNC_FLAG);
}

/**
 * @brief ֹͣ���е��
 */
void StepMotor_Stop(void)
{
    /* ֹͣX���� */
    Emm_V5_Stop_Now(&MOTOR_X_UART, MOTOR_X_ADDR, MOTOR_SYNC_FLAG);

    /* ֹͣY���� */
    Emm_V5_Stop_Now(&MOTOR_Y_UART, MOTOR_Y_ADDR, MOTOR_SYNC_FLAG);
}
/**
 * @brief 控制XY轴电机转动固定的脉冲数
 * @param x_pulses X轴脉冲数，范围可正可负
 * @param y_pulses Y轴脉冲数，范围可正可负
 */
void StepMotor_Move_Pulses(int32_t x_pulses, int32_t y_pulses)
{
    uint8_t x_dir, y_dir;
    uint32_t x_pulse_abs, y_pulse_abs;

    /* 计算X轴方向和脉冲数 */
    if (x_pulses >= 0)
    {
        x_dir = 0; /* CW方向 */
        x_pulse_abs = (uint32_t)x_pulses;
    }
    else
    {
        x_dir = 1; /* CCW方向 */
        x_pulse_abs = (uint32_t)(-x_pulses);
    }

    /* 计算Y轴方向和脉冲数 */
    if (y_pulses >= 0)
    {
        y_dir = 0; /* CW方向 */
        y_pulse_abs = (uint32_t)y_pulses;
    }
    else
    {
        y_dir = 1; /* CCW方向 */
        y_pulse_abs = (uint32_t)(-y_pulses);
    }

    /* 控制Y轴电机进行脉冲运动 - 这里是问题所在，Y和X反了 */
    Emm_V5_Pos_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, MOTOR_MAX_SPEED, MOTOR_ACCEL, y_pulse_abs, false, MOTOR_SYNC_FLAG);

    /* 控制X轴电机进行脉冲运动 - 这里是问题所在，X和Y反了 */
    Emm_V5_Pos_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, MOTOR_MAX_SPEED, MOTOR_ACCEL, x_pulse_abs, false, MOTOR_SYNC_FLAG);
}
void Motor_X_Receive_Data(uint8_t com_data)
{
	uint8_t i;
	Emm_V5_Response_t response;

	// 状态0：等待起始字节(0x01)
	if (motor_x_rx_state == 0 && com_data == MOTOR_START_BYTE_01)
	{
		motor_x_rx_state = 1;								// 进入接收状态
		motor_x_rx_buffer[motor_x_rx_counter++] = com_data; // 存储起始字节
	}
	// 状态1：接收数据内容
	else if (motor_x_rx_state == 1)
	{
		motor_x_rx_buffer[motor_x_rx_counter++] = com_data; // 存储数据字节

		// 如果缓冲区满或收到结束字节，进入校验状态
		if (motor_x_rx_counter >= MOTOR_RX_BUFFER_SIZE || com_data == MOTOR_END_BYTE)
		{
			motor_x_rx_state = 2;
		}
	}

	// 状态2：校验和处理数据包
	if (motor_x_rx_state == 2)
	{
		// 检查结束字节是否正确
		if (motor_x_rx_buffer[motor_x_rx_counter - 1] == MOTOR_END_BYTE)
		{
			// 检查是否为就绪数据包 0x01 0xFD 0x9F
			if (motor_x_rx_buffer[0] == MOTOR_START_BYTE_01 &&
				motor_x_rx_buffer[1] == MOTOR_READY_FLAG_1 &&
				motor_x_rx_buffer[2] == MOTOR_READY_FLAG_2)
			{
				motor01_ready = 1; // X轴电机就绪标志置位
				DEBUG_PRINTF(&huart1, "X轴电机就绪!\r\n");
			}
			// 尝试解析其他类型的数据包
			else if (Emm_V5_Parse_Response(motor_x_rx_buffer, motor_x_rx_counter, &response))
			{
				if (response.valid)
				{
					// 更新X轴电机数据
					motor_x_position = response.position;
					motor_x_encoder = response.encoder;
					motor_x_direction = response.dir;
					motor_x_speed = response.speed;

					DEBUG_PRINTF(&huart1, "X轴: 位置=%ld, 编码器=%ld, 方向=%d, 速度=%d\r\n",
								motor_x_position, motor_x_encoder, motor_x_direction, motor_x_speed);
				}
			}

			// 清空缓冲区
			for (i = 0; i < motor_x_rx_counter; i++)
			{
				motor_x_rx_buffer[i] = 0x00;
			}
		}
		else // 结束字节不正确，数据包无效
		{
			stop_flag_car = 2; // 设置错误标志
			DEBUG_PRINTF(&huart1, "X轴数据包无效!\r\n");
			// 清空缓冲区
			for (i = 0; i < motor_x_rx_counter; i++)
			{
				motor_x_rx_buffer[i] = 0x00;
			}
		}

		// 重置状态机和计数器
		motor_x_rx_state = 0;
		motor_x_rx_counter = 0;
	}
}

/**
 * @brief Y轴电机编码器数据接收处理
 * @param com_data 接收到的数据字节
 */
void Motor_Y_Receive_Data(uint8_t com_data)
{
	uint8_t i;
	Emm_V5_Response_t response;

	// 状态0：等待起始字节(0x01)
	if (motor_y_rx_state == 0 && com_data == MOTOR_START_BYTE_01)
	{
		motor_y_rx_state = 1;								// 进入接收状态
		motor_y_rx_buffer[motor_y_rx_counter++] = com_data; // 存储起始字节
	}
	// 状态1：接收数据内容
	else if (motor_y_rx_state == 1)
	{
		motor_y_rx_buffer[motor_y_rx_counter++] = com_data; // 存储数据字节

		// 如果缓冲区满或收到结束字节，进入校验状态
		if (motor_y_rx_counter >= MOTOR_RX_BUFFER_SIZE || com_data == MOTOR_END_BYTE)
		{
			motor_y_rx_state = 2;
		}
	}

	// 状态2：校验和处理数据包
	if (motor_y_rx_state == 2)
	{
		// 检查结束字节是否正确
		if (motor_y_rx_buffer[motor_y_rx_counter - 1] == MOTOR_END_BYTE)
		{
			// 检查是否为就绪数据包 0x01 0xFD 0x9F
			if (motor_y_rx_buffer[0] == MOTOR_START_BYTE_01 &&
				motor_y_rx_buffer[1] == MOTOR_READY_FLAG_1 &&
				motor_y_rx_buffer[2] == MOTOR_READY_FLAG_2)
			{
				motor02_ready = 1; // Y轴电机就绪标志置位
				DEBUG_PRINTF(&huart1, "Y轴电机就绪!\r\n");
			}
			// 尝试解析其他类型的数据包
			else if (Emm_V5_Parse_Response(motor_y_rx_buffer, motor_y_rx_counter, &response))
			{
				if (response.valid)
				{
					// 更新Y轴电机数据
					motor_y_position = response.position;
					motor_y_encoder = response.encoder;
					motor_y_direction = response.dir;
					motor_y_speed = response.speed;

					DEBUG_PRINTF(&huart1, "Y轴: 位置=%ld, 编码器=%ld, 方向=%d, 速度=%d\r\n",
								motor_y_position, motor_y_encoder, motor_y_direction, motor_y_speed);
				}
			}

			// 清空缓冲区
			for (i = 0; i < motor_y_rx_counter; i++)
			{
				motor_y_rx_buffer[i] = 0x00;
			}
		}
		else // 结束字节不正确，数据包无效
		{
			stop_flag_car = 2; // 设置错误标志
			DEBUG_PRINTF(&huart1, "Y轴数据包无效!\r\n");
			// 清空缓冲区
			for (i = 0; i < motor_y_rx_counter; i++)
			{
				motor_y_rx_buffer[i] = 0x00;
			}
		}

		// 重置状态机和计数器
		motor_y_rx_state = 0;
		motor_y_rx_counter = 0;
	}
}

/**
 * @brief 编码器系统初始化
 */
void StepMotor_Encoder_Init(void)
{
    // 初始化接收状态机
    motor_x_rx_state = 0;
    motor_y_rx_state = 0;
    motor_x_rx_counter = 0;
    motor_y_rx_counter = 0;

    // 初始化状态标志
    motor01_ready = 0;
    motor02_ready = 0;
    stop_flag_car = 0;

    // 初始化脉冲计数变量
    motor_x_position = 0;
    motor_y_position = 0;
    motor_x_encoder = 0;
    motor_y_encoder = 0;
    motor_x_direction = 0;
    motor_y_direction = 0;
    motor_x_speed = 0;
    motor_y_speed = 0;

    // 清空接收缓冲区
    memset(motor_x_rx_buffer, 0, MOTOR_RX_BUFFER_SIZE);
    memset(motor_y_rx_buffer, 0, MOTOR_RX_BUFFER_SIZE);
}

/**
 * @brief 启动串口接收
 */
void StepMotor_Start_Receive(void)
{
    // 启动X轴电机串口接收
    HAL_UART_Receive_IT(&MOTOR_X_UART, &motor_x_rx_byte, 1);

    // 启动Y轴电机串口接收
    HAL_UART_Receive_IT(&MOTOR_Y_UART, &motor_y_rx_byte, 1);
}

/**
 * @brief 检查电机状态
 */
void StepMotor_Check_Status(void)
{
    if (motor01_ready)
    {
        DEBUG_PRINTF(&huart1, "X轴电机已就绪\r\n");
        // 可以开始控制X轴电机
    }

    if (motor02_ready)
    {
        DEBUG_PRINTF(&huart1, "Y轴电机已就绪\r\n");
        // 可以开始控制Y轴电机
    }

    if (stop_flag_car == 2)
    {
        DEBUG_PRINTF(&huart1, "检测到通信错误，小车已停止\r\n");
        // 处理错误情况
        StepMotor_Stop();  // 停止所有电机
        stop_flag_car = 0; // 重置错误标志
    }
}

/**
 * @brief 获取X轴位置(脉冲数)
 * @return X轴当前位置
 */
int32_t StepMotor_Get_X_Position(void)
{
    return motor_x_position;
}

/**
 * @brief 获取Y轴位置(脉冲数)
 * @return Y轴当前位置
 */
int32_t StepMotor_Get_Y_Position(void)
{
    return motor_y_position;
}

/**
 * @brief 获取X轴编码器值
 * @return X轴编码器值
 */
int32_t StepMotor_Get_X_Encoder(void)
{
    return motor_x_encoder;
}

/**
 * @brief 获取Y轴编码器值
 * @return Y轴编码器值
 */
int32_t StepMotor_Get_Y_Encoder(void)
{
    return motor_y_encoder;
}

/**
 * @brief 重置位置计数器
 */
void StepMotor_Reset_Position(void)
{
    motor_x_position = 0;
    motor_y_position = 0;
    motor_x_encoder = 0;
    motor_y_encoder = 0;
    DEBUG_PRINTF(&huart1, "位置计数器已重置\r\n");
}

/**
 * @brief 打印电机状态信息
 */
void StepMotor_Print_Status(void)
{
    DEBUG_PRINTF(&huart1, "=== 步进电机状态 ===\r\n");
    DEBUG_PRINTF(&huart1, "X轴就绪: %s\r\n", motor01_ready ? "是" : "否");
    DEBUG_PRINTF(&huart1, "Y轴就绪: %s\r\n", motor02_ready ? "是" : "否");
    DEBUG_PRINTF(&huart1, "X轴位置: %ld 脉冲\r\n", motor_x_position);
    DEBUG_PRINTF(&huart1, "Y轴位置: %ld 脉冲\r\n", motor_y_position);
    DEBUG_PRINTF(&huart1, "X轴编码器: %ld\r\n", motor_x_encoder);
    DEBUG_PRINTF(&huart1, "Y轴编码器: %ld\r\n", motor_y_encoder);
    DEBUG_PRINTF(&huart1, "X轴方向: %s\r\n", motor_x_direction ? "CCW" : "CW");
    DEBUG_PRINTF(&huart1, "Y轴方向: %s\r\n", motor_y_direction ? "CCW" : "CW");
    DEBUG_PRINTF(&huart1, "X轴速度: %d RPM\r\n", motor_x_speed);
    DEBUG_PRINTF(&huart1, "Y轴速度: %d RPM\r\n", motor_y_speed);
    DEBUG_PRINTF(&huart1, "错误标志: %d\r\n", stop_flag_car);
    DEBUG_PRINTF(&huart1, "==================\r\n");
}

/**
 * @brief 主动请求位置信息
 */
void StepMotor_Request_Position(void)
{
    // 请求X轴位置 (地址1，功能码0x36)
    Emm_V5_Read_Realtime_Pos(1);
    HAL_Delay(10); // 短暂延时

    // 请求Y轴位置 (地址2，功能码0x36)
    Emm_V5_Read_Realtime_Pos(2);
    HAL_Delay(10); // 短暂延时
}

/**
 * @brief 主动请求编码器信息
 */
void StepMotor_Request_Encoder(void)
{
    // 请求X轴编码器值 (地址1，功能码0x31)
    Emm_V5_Read_Encoder(1);
    HAL_Delay(10); // 短暂延时

    // 请求Y轴编码器值 (地址2，功能码0x31)
    Emm_V5_Read_Encoder(2);
    HAL_Delay(10); // 短暂延时
}
