#include "MyDefine.h"
#include "StepMotor_app.h"
#include <math.h>
#include <stdlib.h>

/* 串口接收字节变量定义 */
uint8_t motor_x_rx_byte = 0;
uint8_t motor_y_rx_byte = 0;

/* 编码器接收相关变量定义 */
uint8_t motor_x_rx_state = 0;                       // X轴接收状态机状态
uint8_t motor_x_rx_buffer[MOTOR_RX_BUFFER_SIZE];     // X轴接收缓冲区
uint8_t motor_x_rx_counter = 0;                     // X轴接收计数器
uint8_t motor_y_rx_state = 0;                       // Y轴接收状态机状态
uint8_t motor_y_rx_buffer[MOTOR_RX_BUFFER_SIZE];     // Y轴接收缓冲区
uint8_t motor_y_rx_counter = 0;                     // Y轴接收计数器

/* 电机状态标志定义 */
uint8_t motor01_ready = 0;                          // X轴电机就绪标志
uint8_t motor02_ready = 0;                          // Y轴电机就绪标志
uint8_t stop_flag_car = 0;                          // 小车停止标志
/**
 * @brief �����ʼ������
 */
void StepMotor_Init(void)
{
    /* ʹ��X���� */
    Emm_V5_En_Control(&MOTOR_X_UART, MOTOR_X_ADDR, true, MOTOR_SYNC_FLAG);

    /* ʹ��Y���� */
    Emm_V5_En_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, true, MOTOR_SYNC_FLAG);

    /* ��ʼֹͣ */
    StepMotor_Stop();
}

/**
 * @brief ����XY�����ٶ�
 * @param x_percent X���ٶȰٷֱȣ���Χ-100��100
 * @param y_percent Y���ٶȰٷֱȣ���Χ-100��100
 */
void StepMotor_Set_Speed(int8_t x_percent, int8_t y_percent)
{
    uint8_t x_dir, y_dir;
    uint16_t x_speed, y_speed;

    /* ���ưٷֱȷ�Χ */
    if (x_percent > 100)
        x_percent = 100;
    if (x_percent < -100)
        x_percent = -100;
    if (y_percent > 100)
        y_percent = 100;
    if (y_percent < -100)
        y_percent = -100;

    /* ����X�᷽�� */
    if (x_percent >= 0)
    {
        x_dir = 0; /* CW���� */
    }
    else
    {
        x_dir = 1;              /* CCW���� */
        x_percent = -x_percent; /* ȡ����ֵ */
    }

    /* ����Y�᷽�� */
    if (y_percent >= 0)
    {
        y_dir = 0; /* CW���� */
    }
    else
    {
        y_dir = 1;              /* CCW���� */
        y_percent = -y_percent; /* ȡ����ֵ */
    }

    /* ����ʵ���ٶ�ֵ(�ٷֱ�ת��ΪRPM) */
    x_speed = (uint16_t)((x_percent * MOTOR_MAX_SPEED) / 100);
    y_speed = (uint16_t)((y_percent * MOTOR_MAX_SPEED) / 100);

    /* ����X���� */
    Emm_V5_Vel_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, x_speed, MOTOR_ACCEL, MOTOR_SYNC_FLAG);

    /* ����Y���� */
    Emm_V5_Vel_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, y_speed, MOTOR_ACCEL, MOTOR_SYNC_FLAG);
}

/**
 * @brief ֹͣ���е��
 */
void StepMotor_Stop(void)
{
    /* ֹͣX���� */
    Emm_V5_Stop_Now(&MOTOR_X_UART, MOTOR_X_ADDR, MOTOR_SYNC_FLAG);

    /* ֹͣY���� */
    Emm_V5_Stop_Now(&MOTOR_Y_UART, MOTOR_Y_ADDR, MOTOR_SYNC_FLAG);
}
/**
 * @brief 控制XY轴电机转动固定的脉冲数
 * @param x_pulses X轴脉冲数，范围可正可负
 * @param y_pulses Y轴脉冲数，范围可正可负
 */
void StepMotor_Move_Pulses(int32_t x_pulses, int32_t y_pulses)
{
    uint8_t x_dir, y_dir;
    uint32_t x_pulse_abs, y_pulse_abs;

    /* 计算X轴方向和脉冲数 */
    if (x_pulses >= 0)
    {
        x_dir = 0; /* CW方向 */
        x_pulse_abs = (uint32_t)x_pulses;
    }
    else
    {
        x_dir = 1; /* CCW方向 */
        x_pulse_abs = (uint32_t)(-x_pulses);
    }

    /* 计算Y轴方向和脉冲数 */
    if (y_pulses >= 0)
    {
        y_dir = 0; /* CW方向 */
        y_pulse_abs = (uint32_t)y_pulses;
    }
    else
    {
        y_dir = 1; /* CCW方向 */
        y_pulse_abs = (uint32_t)(-y_pulses);
    }

    /* 控制Y轴电机进行脉冲运动 - 这里是问题所在，Y和X反了 */
    Emm_V5_Pos_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, MOTOR_MAX_SPEED, MOTOR_ACCEL, y_pulse_abs, false, MOTOR_SYNC_FLAG);

    /* 控制X轴电机进行脉冲运动 - 这里是问题所在，X和Y反了 */
    Emm_V5_Pos_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, MOTOR_MAX_SPEED, MOTOR_ACCEL, x_pulse_abs, false, MOTOR_SYNC_FLAG);
}
void Motor_X_Receive_Data(uint8_t com_data)
{
	uint8_t i;

	// ??0???????????(0x01)
	if (motor_x_rx_state == 0 && com_data == MOTOR_START_BYTE_01)
	{
		motor_x_rx_state = 1;								// ?????????????
		motor_x_rx_buffer[motor_x_rx_counter++] = com_data; // ?洢??????
	}
	// ??1??????????????
	else if (motor_x_rx_state == 1)
	{
		motor_x_rx_buffer[motor_x_rx_counter++] = com_data; // ?洢??????

		// ??????????10??????????????0x6B???????У????
		if (motor_x_rx_counter >= MOTOR_RX_BUFFER_SIZE || com_data == MOTOR_END_BYTE)
		{
			motor_x_rx_state = 2;
		}
	}

	// ??2??У?????????????
	if (motor_x_rx_state == 2)
	{
		// ????β????0x6B?????????Ч??
		if (motor_x_rx_buffer[motor_x_rx_counter - 1] == MOTOR_END_BYTE)
		{
			// ???0x01??????????λ???0xFD 0x9F
			if (motor_x_rx_buffer[0] == MOTOR_START_BYTE_01 &&
				motor_x_rx_buffer[1] == MOTOR_READY_FLAG_1 &&
				motor_x_rx_buffer[2] == MOTOR_READY_FLAG_2)
			{
				motor01_ready = 1; // X??????λ?????λ
				DEBUG_PRINTF(&huart1, "X??????λ!\r\n");
			}

			// ????????
			for (i = 0; i < motor_x_rx_counter; i++)
			{
				motor_x_rx_buffer[i] = 0x00;
			}
		}
		else // ?β????0x6B?????????Ч??
		{
			stop_flag_car = 2; // ?????????
			DEBUG_PRINTF(&huart1, "X?????????Ч!\r\n");
			// ????????
			for (i = 0; i < motor_x_rx_counter; i++)
			{
				motor_x_rx_buffer[i] = 0x00;
			}
		}

		// ?????????????????????
		motor_x_rx_state = 0;
		motor_x_rx_counter = 0;
	}
}

/**
 * @brief Y??????????????????
 * @param com_data ???????????????
 */
void Motor_Y_Receive_Data(uint8_t com_data)
{
	uint8_t i;

	// ??0???????????(0x01)
	if (motor_y_rx_state == 0 && com_data == MOTOR_START_BYTE_01)
	{
		motor_y_rx_state = 1;								// ?????????????
		motor_y_rx_buffer[motor_y_rx_counter++] = com_data; // ?洢??????
	}
	// ??1??????????????
	else if (motor_y_rx_state == 1)
	{
		motor_y_rx_buffer[motor_y_rx_counter++] = com_data; // ?洢??????

		// ??????????10??????????????0x6B???????У????
		if (motor_y_rx_counter >= MOTOR_RX_BUFFER_SIZE || com_data == MOTOR_END_BYTE)
		{
			motor_y_rx_state = 2;
		}
	}

	// ??2??У?????????????
	if (motor_y_rx_state == 2)
	{
		// ????β????0x6B?????????Ч??
		if (motor_y_rx_buffer[motor_y_rx_counter - 1] == MOTOR_END_BYTE)
		{
			// ???0x01??????????λ???0xFD 0x9F
			if (motor_y_rx_buffer[0] == MOTOR_START_BYTE_01 &&
				motor_y_rx_buffer[1] == MOTOR_READY_FLAG_1 &&
				motor_y_rx_buffer[2] == MOTOR_READY_FLAG_2)
			{
				motor02_ready = 1; // Y??????λ?????λ
				DEBUG_PRINTF(&huart1, "Y??????λ!\r\n");
			}

			// ????????
			for (i = 0; i < motor_y_rx_counter; i++)
			{
				motor_y_rx_buffer[i] = 0x00;
			}
		}
		else // ?β????0x6B?????????Ч??
		{
			stop_flag_car = 2; // ?????????
			DEBUG_PRINTF(&huart1, "Y?????????Ч!\r\n");
			// ????????
			for (i = 0; i < motor_y_rx_counter; i++)
			{
				motor_y_rx_buffer[i] = 0x00;
			}
		}

		// ?????????????????????
		motor_y_rx_state = 0;
		motor_y_rx_counter = 0;
	}
}

/**
 * @brief 编码器系统初始化
 */
void StepMotor_Encoder_Init(void)
{
    // 初始化接收状态机
    motor_x_rx_state = 0;
    motor_y_rx_state = 0;
    motor_x_rx_counter = 0;
    motor_y_rx_counter = 0;

    // 初始化状态标志
    motor01_ready = 0;
    motor02_ready = 0;
    stop_flag_car = 0;

    // 清空接收缓冲区
    memset(motor_x_rx_buffer, 0, MOTOR_RX_BUFFER_SIZE);
    memset(motor_y_rx_buffer, 0, MOTOR_RX_BUFFER_SIZE);
}

/**
 * @brief 启动串口接收
 */
void StepMotor_Start_Receive(void)
{
    // 启动X轴电机串口接收
    HAL_UART_Receive_IT(&MOTOR_X_UART, &motor_x_rx_byte, 1);

    // 启动Y轴电机串口接收
    HAL_UART_Receive_IT(&MOTOR_Y_UART, &motor_y_rx_byte, 1);
}

/**
 * @brief 检查电机状态
 */
void StepMotor_Check_Status(void)
{
    if (motor01_ready)
    {
        DEBUG_PRINTF(&huart1, "X轴电机已就绪\r\n");
        // 可以开始控制X轴电机
    }

    if (motor02_ready)
    {
        DEBUG_PRINTF(&huart1, "Y轴电机已就绪\r\n");
        // 可以开始控制Y轴电机
    }

    if (stop_flag_car == 2)
    {
        DEBUG_PRINTF(&huart1, "检测到通信错误，小车已停止\r\n");
        // 处理错误情况
        StepMotor_Stop();  // 停止所有电机
        stop_flag_car = 0; // 重置错误标志
    }
}
