#include "MyDefine.h"
#include "StepMotor_app.h"
#include <math.h>
#include <stdlib.h>

uint8_t motor_x_rx_byte = 0;
uint8_t motor_y_rx_byte = 0;
/**
 * @brief �����ʼ������
 */
void StepMotor_Init(void)
{
    /* ʹ��X���� */
    Emm_V5_En_Control(&MOTOR_X_UART, MOTOR_X_ADDR, true, MOTOR_SYNC_FLAG);

    /* ʹ��Y���� */
    Emm_V5_En_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, true, MOTOR_SYNC_FLAG);

    /* ��ʼֹͣ */
    StepMotor_Stop();
}

/**
 * @brief ����XY�����ٶ�
 * @param x_percent X���ٶȰٷֱȣ���Χ-100��100
 * @param y_percent Y���ٶȰٷֱȣ���Χ-100��100
 */
void StepMotor_Set_Speed(int8_t x_percent, int8_t y_percent)
{
    uint8_t x_dir, y_dir;
    uint16_t x_speed, y_speed;

    /* ���ưٷֱȷ�Χ */
    if (x_percent > 100)
        x_percent = 100;
    if (x_percent < -100)
        x_percent = -100;
    if (y_percent > 100)
        y_percent = 100;
    if (y_percent < -100)
        y_percent = -100;

    /* ����X�᷽�� */
    if (x_percent >= 0)
    {
        x_dir = 0; /* CW���� */
    }
    else
    {
        x_dir = 1;              /* CCW���� */
        x_percent = -x_percent; /* ȡ����ֵ */
    }

    /* ����Y�᷽�� */
    if (y_percent >= 0)
    {
        y_dir = 0; /* CW���� */
    }
    else
    {
        y_dir = 1;              /* CCW���� */
        y_percent = -y_percent; /* ȡ����ֵ */
    }

    /* ����ʵ���ٶ�ֵ(�ٷֱ�ת��ΪRPM) */
    x_speed = (uint16_t)((x_percent * MOTOR_MAX_SPEED) / 100);
    y_speed = (uint16_t)((y_percent * MOTOR_MAX_SPEED) / 100);

    /* ����X���� */
    Emm_V5_Vel_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, x_speed, MOTOR_ACCEL, MOTOR_SYNC_FLAG);

    /* ����Y���� */
    Emm_V5_Vel_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, y_speed, MOTOR_ACCEL, MOTOR_SYNC_FLAG);
}

/**
 * @brief ֹͣ���е��
 */
void StepMotor_Stop(void)
{
    /* ֹͣX���� */
    Emm_V5_Stop_Now(&MOTOR_X_UART, MOTOR_X_ADDR, MOTOR_SYNC_FLAG);

    /* ֹͣY���� */
    Emm_V5_Stop_Now(&MOTOR_Y_UART, MOTOR_Y_ADDR, MOTOR_SYNC_FLAG);
}
/**
 * @brief 控制XY轴电机转动固定的脉冲数
 * @param x_pulses X轴脉冲数，范围可正可负
 * @param y_pulses Y轴脉冲数，范围可正可负
 */
void StepMotor_Move_Pulses(int32_t x_pulses, int32_t y_pulses)
{
    uint8_t x_dir, y_dir;
    uint32_t x_pulse_abs, y_pulse_abs;

    /* 计算X轴方向和脉冲数 */
    if (x_pulses >= 0)
    {
        x_dir = 0; /* CW方向 */
        x_pulse_abs = (uint32_t)x_pulses;
    }
    else
    {
        x_dir = 1; /* CCW方向 */
        x_pulse_abs = (uint32_t)(-x_pulses);
    }

    /* 计算Y轴方向和脉冲数 */
    if (y_pulses >= 0)
    {
        y_dir = 0; /* CW方向 */
        y_pulse_abs = (uint32_t)y_pulses;
    }
    else
    {
        y_dir = 1; /* CCW方向 */
        y_pulse_abs = (uint32_t)(-y_pulses);
    }

    /* 控制Y轴电机进行脉冲运动 - 这里是问题所在，Y和X反了 */
    Emm_V5_Pos_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, MOTOR_MAX_SPEED, MOTOR_ACCEL, y_pulse_abs, false, MOTOR_SYNC_FLAG);

    /* 控制X轴电机进行脉冲运动 - 这里是问题所在，X和Y反了 */
    Emm_V5_Pos_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, MOTOR_MAX_SPEED, MOTOR_ACCEL, x_pulse_abs, false, MOTOR_SYNC_FLAG);
}
void Motor_X_Receive_Data(uint8_t com_data)
{
	uint8_t i;

	// ??0???????????(0x01)
	if (motor_x_rx_state == 0 && com_data == MOTOR_START_BYTE_01)
	{
		motor_x_rx_state = 1;								// ?????????????
		motor_x_rx_buffer[motor_x_rx_counter++] = com_data; // ?洢??????
	}
	// ??1??????????????
	else if (motor_x_rx_state == 1)
	{
		motor_x_rx_buffer[motor_x_rx_counter++] = com_data; // ?洢??????

		// ??????????10??????????????0x6B???????У????
		if (motor_x_rx_counter >= MOTOR_RX_BUFFER_SIZE || com_data == MOTOR_END_BYTE)
		{
			motor_x_rx_state = 2;
		}
	}

	// ??2??У?????????????
	if (motor_x_rx_state == 2)
	{
		// ????β????0x6B?????????Ч??
		if (motor_x_rx_buffer[motor_x_rx_counter - 1] == MOTOR_END_BYTE)
		{
			// ???0x01??????????λ???0xFD 0x9F
			if (motor_x_rx_buffer[0] == MOTOR_START_BYTE_01 &&
				motor_x_rx_buffer[1] == MOTOR_READY_FLAG_1 &&
				motor_x_rx_buffer[2] == MOTOR_READY_FLAG_2)
			{
				motor01_ready = 1; // X??????λ?????λ
				DEBUG_PRINTF(&huart1, "X??????λ!\r\n");
			}

			// ????????
			for (i = 0; i < motor_x_rx_counter; i++)
			{
				motor_x_rx_buffer[i] = 0x00;
			}
		}
		else // ?β????0x6B?????????Ч??
		{
			stop_flag_car = 2; // ?????????
			DEBUG_PRINTF(&huart1, "X?????????Ч!\r\n");
			// ????????
			for (i = 0; i < motor_x_rx_counter; i++)
			{
				motor_x_rx_buffer[i] = 0x00;
			}
		}

		// ?????????????????????
		motor_x_rx_state = 0;
		motor_x_rx_counter = 0;
	}
}

/**
 * @brief Y??????????????????
 * @param com_data ???????????????
 */
void Motor_Y_Receive_Data(uint8_t com_data)
{
	uint8_t i;

	// ??0???????????(0x01)
	if (motor_y_rx_state == 0 && com_data == MOTOR_START_BYTE_01)
	{
		motor_y_rx_state = 1;								// ?????????????
		motor_y_rx_buffer[motor_y_rx_counter++] = com_data; // ?洢??????
	}
	// ??1??????????????
	else if (motor_y_rx_state == 1)
	{
		motor_y_rx_buffer[motor_y_rx_counter++] = com_data; // ?洢??????

		// ??????????10??????????????0x6B???????У????
		if (motor_y_rx_counter >= MOTOR_RX_BUFFER_SIZE || com_data == MOTOR_END_BYTE)
		{
			motor_y_rx_state = 2;
		}
	}

	// ??2??У?????????????
	if (motor_y_rx_state == 2)
	{
		// ????β????0x6B?????????Ч??
		if (motor_y_rx_buffer[motor_y_rx_counter - 1] == MOTOR_END_BYTE)
		{
			// ???0x01??????????λ???0xFD 0x9F
			if (motor_y_rx_buffer[0] == MOTOR_START_BYTE_01 &&
				motor_y_rx_buffer[1] == MOTOR_READY_FLAG_1 &&
				motor_y_rx_buffer[2] == MOTOR_READY_FLAG_2)
			{
				motor02_ready = 1; // Y??????λ?????λ
				DEBUG_PRINTF(&huart1, "Y??????λ!\r\n");
			}

			// ????????
			for (i = 0; i < motor_y_rx_counter; i++)
			{
				motor_y_rx_buffer[i] = 0x00;
			}
		}
		else // ?β????0x6B?????????Ч??
		{
			stop_flag_car = 2; // ?????????
			DEBUG_PRINTF(&huart1, "Y?????????Ч!\r\n");
			// ????????
			for (i = 0; i < motor_y_rx_counter; i++)
			{
				motor_y_rx_buffer[i] = 0x00;
			}
		}

		// ?????????????????????
		motor_y_rx_state = 0;
		motor_y_rx_counter = 0;
	}
}
