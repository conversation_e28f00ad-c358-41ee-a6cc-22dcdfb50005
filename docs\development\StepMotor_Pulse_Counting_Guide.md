# 步进电机脉冲计数使用指南

## 🎯 功能概述

现在您的系统可以实时监控步进电机的脉冲数了！系统会自动解析从电机返回的位置和编码器数据。

## 📊 可获取的数据

### 1. 位置数据 (脉冲数)
```c
int32_t x_pulses = StepMotor_Get_X_Position();  // 获取X轴脉冲数
int32_t y_pulses = StepMotor_Get_Y_Position();  // 获取Y轴脉冲数
```

### 2. 编码器数据
```c
int32_t x_encoder = StepMotor_Get_X_Encoder();  // 获取X轴编码器值
int32_t y_encoder = StepMotor_Get_Y_Encoder();  // 获取Y轴编码器值
```

### 3. 直接访问全局变量
```c
extern int32_t motor_x_position;    // X轴当前位置(脉冲数)
extern int32_t motor_y_position;    // Y轴当前位置(脉冲数)
extern int32_t motor_x_encoder;     // X轴编码器值
extern int32_t motor_y_encoder;     // Y轴编码器值
extern uint8_t motor_x_direction;   // X轴方向(0=CW, 1=CCW)
extern uint8_t motor_y_direction;   // Y轴方向(0=CW, 1=CCW)
extern int16_t motor_x_speed;       // X轴实时速度(RPM)
extern int16_t motor_y_speed;       // Y轴实时速度(RPM)
```

## 🔄 数据更新方式

### 自动更新 (推荐)
系统会在您的 `Uart_Task()` 中自动解析接收到的数据包，无需额外操作。

### 主动请求
如果需要主动获取最新数据：
```c
void Get_Latest_Motor_Data(void)
{
    // 请求位置信息
    StepMotor_Request_Position();
    HAL_Delay(50);  // 等待响应
    
    // 请求编码器信息  
    StepMotor_Request_Encoder();
    HAL_Delay(50);  // 等待响应
    
    // 现在可以读取最新数据
    int32_t x_pos = StepMotor_Get_X_Position();
    int32_t y_pos = StepMotor_Get_Y_Position();
    
    printf("X轴: %ld 脉冲, Y轴: %ld 脉冲\r\n", x_pos, y_pos);
}
```

## 📋 实用示例

### 示例1: 监控电机运动
```c
void Monitor_Motor_Movement(void)
{
    static int32_t last_x_pos = 0;
    static int32_t last_y_pos = 0;
    
    int32_t current_x = StepMotor_Get_X_Position();
    int32_t current_y = StepMotor_Get_Y_Position();
    
    // 检查是否有运动
    if (current_x != last_x_pos || current_y != last_y_pos)
    {
        int32_t x_moved = current_x - last_x_pos;
        int32_t y_moved = current_y - last_y_pos;
        
        printf("电机移动: X轴 %ld 脉冲, Y轴 %ld 脉冲\r\n", x_moved, y_moved);
        printf("当前位置: X=%ld, Y=%ld\r\n", current_x, current_y);
        
        last_x_pos = current_x;
        last_y_pos = current_y;
    }
}
```

### 示例2: 精确定位控制
```c
void Move_To_Position(int32_t target_x, int32_t target_y)
{
    int32_t current_x = StepMotor_Get_X_Position();
    int32_t current_y = StepMotor_Get_Y_Position();
    
    int32_t x_diff = target_x - current_x;
    int32_t y_diff = target_y - current_y;
    
    printf("需要移动: X轴 %ld 脉冲, Y轴 %ld 脉冲\r\n", x_diff, y_diff);
    
    // 使用您现有的电机控制函数
    if (abs(x_diff) > 10 || abs(y_diff) > 10)  // 误差大于10脉冲才移动
    {
        StepMotor_Move_Pulses(x_diff, y_diff);
        
        // 等待运动完成后检查位置
        HAL_Delay(1000);
        StepMotor_Request_Position();  // 请求最新位置
        HAL_Delay(100);
        
        int32_t final_x = StepMotor_Get_X_Position();
        int32_t final_y = StepMotor_Get_Y_Position();
        
        printf("最终位置: X=%ld, Y=%ld\r\n", final_x, final_y);
    }
}
```

### 示例3: 定时状态报告
```c
void Motor_Status_Task(void)
{
    static uint32_t last_report_time = 0;
    uint32_t current_time = HAL_GetTick();
    
    // 每5秒报告一次状态
    if (current_time - last_report_time >= 5000)
    {
        StepMotor_Print_Status();  // 打印完整状态
        last_report_time = current_time;
    }
}
```

### 示例4: 脉冲计数器重置
```c
void Reset_And_Home(void)
{
    // 重置脉冲计数器
    StepMotor_Reset_Position();
    
    // 可选：移动到原点位置
    StepMotor_Move_To_Origin();
    
    printf("电机已回到原点，脉冲计数器已重置\r\n");
}
```

## 🔧 在您的主循环中使用

```c
void Your_Main_Task(void)
{
    // 检查电机就绪状态
    if (motor01_ready && motor02_ready)
    {
        // 获取当前位置
        int32_t x_pos = motor_x_position;  // 直接访问全局变量
        int32_t y_pos = motor_y_position;
        
        // 或者使用函数获取
        // int32_t x_pos = StepMotor_Get_X_Position();
        // int32_t y_pos = StepMotor_Get_Y_Position();
        
        // 根据位置做决策
        if (abs(x_pos) > 10000)  // X轴移动超过10000脉冲
        {
            printf("X轴位置: %ld 脉冲，可能需要回中\r\n", x_pos);
        }
        
        // 监控运动方向和速度
        if (motor_x_speed != 0 || motor_y_speed != 0)
        {
            printf("电机运动中: X速度=%d RPM, Y速度=%d RPM\r\n", 
                   motor_x_speed, motor_y_speed);
        }
    }
}
```

## 📈 调试输出

系统会自动输出调试信息到串口1：
- `"X轴: 位置=1000, 编码器=1000, 方向=0, 速度=50"`
- `"Y轴: 位置=-500, 编码器=500, 方向=1, 速度=30"`

## ⚠️ 注意事项

1. **数据更新频率**: 数据在每次 `Uart_Task()` 调用时更新
2. **方向标志**: 0=顺时针(CW), 1=逆时针(CCW)
3. **脉冲计数**: 可能为负数，表示反向运动
4. **编码器值**: 通常为绝对位置值
5. **主动请求**: 使用 `StepMotor_Request_Position()` 获取最新数据

## 🎉 总结

现在您可以：
- ✅ 实时查看电机转了多少脉冲
- ✅ 监控电机运动方向和速度  
- ✅ 实现精确的位置控制
- ✅ 检测电机是否在运动
- ✅ 重置脉冲计数器

所有数据都会自动更新，您只需要读取相应的变量或调用查询函数即可！
