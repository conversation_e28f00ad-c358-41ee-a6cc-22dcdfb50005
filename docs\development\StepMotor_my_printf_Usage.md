# 使用my_printf查看步进电机脉冲数

## ✅ 已完成的修改

现在系统已经完全使用您的 `my_printf` 函数进行调试输出了！

### 修改内容：
- ✅ 将 `DEBUG_PRINTF` 宏定义改为使用您的 `my_printf` 函数
- ✅ 保持了您现有的代码结构完全不变
- ✅ 所有调试信息都会通过串口1输出

## 🔍 自动输出的调试信息

系统现在会自动通过串口1输出以下信息：

### 1. 电机就绪信息
```
X轴电机就绪!
Y轴电机就绪!
```

### 2. 实时脉冲和状态信息
```
X轴: 位置=1000, 编码器=1000, 方向=0, 速度=50
Y轴: 位置=-500, 编码器=500, 方向=1, 速度=30
```

### 3. 错误信息
```
X轴数据包无效!
Y轴数据包无效!
检测到通信错误，小车已停止
```

## 📊 手动查看脉冲数的方法

### 方法1: 在您的代码中直接使用my_printf
```c
void Check_Motor_Pulses(void)
{
    // 显示当前脉冲数
    my_printf(&huart1, "当前脉冲数 - X轴: %ld, Y轴: %ld\r\n", 
              motor_x_position, motor_y_position);
    
    // 显示编码器值
    my_printf(&huart1, "编码器值 - X轴: %ld, Y轴: %ld\r\n", 
              motor_x_encoder, motor_y_encoder);
    
    // 显示运动方向
    my_printf(&huart1, "运动方向 - X轴: %s, Y轴: %s\r\n", 
              motor_x_direction ? "逆时针" : "顺时针",
              motor_y_direction ? "逆时针" : "顺时针");
    
    // 显示实时速度
    my_printf(&huart1, "实时速度 - X轴: %d RPM, Y轴: %d RPM\r\n", 
              motor_x_speed, motor_y_speed);
}
```

### 方法2: 使用系统提供的状态打印函数
```c
void Show_Complete_Status(void)
{
    // 这个函数内部使用my_printf输出完整状态
    StepMotor_Print_Status();
}
```

### 方法3: 定时监控脉冲变化
```c
void Monitor_Pulse_Changes(void)
{
    static int32_t last_x_pulses = 0;
    static int32_t last_y_pulses = 0;
    static uint32_t last_check_time = 0;
    
    uint32_t current_time = HAL_GetTick();
    
    // 每1秒检查一次
    if (current_time - last_check_time >= 1000)
    {
        int32_t current_x = motor_x_position;
        int32_t current_y = motor_y_position;
        
        // 计算脉冲变化
        int32_t x_change = current_x - last_x_pulses;
        int32_t y_change = current_y - last_y_pulses;
        
        if (x_change != 0 || y_change != 0)
        {
            my_printf(&huart1, "脉冲变化: X轴 %+ld, Y轴 %+ld\r\n", x_change, y_change);
            my_printf(&huart1, "总脉冲数: X轴 %ld, Y轴 %ld\r\n", current_x, current_y);
        }
        
        last_x_pulses = current_x;
        last_y_pulses = current_y;
        last_check_time = current_time;
    }
}
```

## 🎯 在您的主循环中使用

### 在Uart_Task中添加脉冲监控
您可以在现有的 `Uart_Task()` 函数中添加脉冲监控：

```c
void Uart_Task(void)
{
    // ... 您现有的代码 ...
    
    // 添加脉冲监控 (可选)
    static uint32_t last_pulse_check = 0;
    uint32_t current_time = HAL_GetTick();
    
    if (current_time - last_pulse_check >= 5000)  // 每5秒显示一次
    {
        if (motor01_ready && motor02_ready)
        {
            my_printf(&huart1, "=== 脉冲状态 ===\r\n");
            my_printf(&huart1, "X轴: %ld 脉冲, Y轴: %ld 脉冲\r\n", 
                      motor_x_position, motor_y_position);
            my_printf(&huart1, "===============\r\n");
        }
        last_pulse_check = current_time;
    }
}
```

### 在主循环中添加状态检查
```c
void Your_Main_Loop(void)
{
    while(1)
    {
        // 您现有的任务调度
        Scheduler_Run();
        
        // 添加脉冲监控 (可选)
        Monitor_Pulse_Changes();
        
        // 检查电机状态
        if (motor01_ready && motor02_ready)
        {
            // 可以在这里添加基于脉冲数的控制逻辑
            if (abs(motor_x_position) > 5000)
            {
                my_printf(&huart1, "警告: X轴位置过大 (%ld 脉冲)\r\n", motor_x_position);
            }
        }
    }
}
```

## 🔧 实用的调试命令

### 重置脉冲计数器
```c
void Reset_Pulse_Counter(void)
{
    StepMotor_Reset_Position();  // 内部使用my_printf输出确认信息
    my_printf(&huart1, "脉冲计数器已重置为0\r\n");
}
```

### 主动请求最新数据
```c
void Request_Latest_Data(void)
{
    my_printf(&huart1, "正在请求最新位置数据...\r\n");
    StepMotor_Request_Position();
    HAL_Delay(100);  // 等待响应
    
    my_printf(&huart1, "正在请求最新编码器数据...\r\n");
    StepMotor_Request_Encoder();
    HAL_Delay(100);  // 等待响应
    
    my_printf(&huart1, "数据请求完成\r\n");
}
```

## 📈 串口输出示例

当您运行系统时，串口1会输出类似这样的信息：

```
X轴电机就绪!
Y轴电机就绪!
X轴: 位置=0, 编码器=0, 方向=0, 速度=0
Y轴: 位置=0, 编码器=0, 方向=0, 速度=0
=== 脉冲状态 ===
X轴: 1250 脉冲, Y轴: -800 脉冲
===============
脉冲变化: X轴 +1250, Y轴 -800
总脉冲数: X轴 1250, Y轴 -800
```

## ⚡ 优势

1. **使用您熟悉的函数**: 完全使用您的 `my_printf` 函数
2. **保持代码一致性**: 与您现有的代码风格完全一致
3. **自动输出**: 系统会自动输出重要的状态信息
4. **灵活控制**: 您可以随时添加自定义的调试输出
5. **实时监控**: 可以实时看到脉冲数的变化

## 🎉 总结

现在您可以：
- ✅ 通过串口1实时查看电机脉冲数
- ✅ 使用熟悉的 `my_printf` 函数进行调试
- ✅ 系统自动输出重要状态信息
- ✅ 随时添加自定义的脉冲监控代码

**所有调试输出都使用您的my_printf函数，完美集成到您的现有系统中！** 🚀
