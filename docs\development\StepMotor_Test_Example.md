# 步进电机脉冲计数测试示例

## ✅ 编译错误已修复

语法错误已经成功修复！现在代码可以正常编译了。

### 修复的问题：
- ✅ 修复了DEBUG_PRINTF函数调用中的括号匹配问题
- ✅ 确保了多行参数的正确格式
- ✅ 所有语法错误已清除

## 🧪 测试代码示例

### 1. 在主循环中添加脉冲监控测试

您可以在主循环中添加以下测试代码：

```c
void Test_Pulse_Monitoring(void)
{
    static uint32_t last_test_time = 0;
    uint32_t current_time = HAL_GetTick();
    
    // 每3秒执行一次测试
    if (current_time - last_test_time >= 3000)
    {
        // 检查电机就绪状态
        if (motor01_ready && motor02_ready)
        {
            my_printf(&huart1, "\r\n=== 脉冲监控测试 ===\r\n");
            
            // 显示当前脉冲数
            my_printf(&huart1, "当前脉冲数:\r\n");
            my_printf(&huart1, "  X轴: %ld 脉冲\r\n", motor_x_position);
            my_printf(&huart1, "  Y轴: %ld 脉冲\r\n", motor_y_position);
            
            // 显示编码器值
            my_printf(&huart1, "编码器值:\r\n");
            my_printf(&huart1, "  X轴: %ld\r\n", motor_x_encoder);
            my_printf(&huart1, "  Y轴: %ld\r\n", motor_y_encoder);
            
            // 显示运动状态
            my_printf(&huart1, "运动状态:\r\n");
            my_printf(&huart1, "  X轴: %s, 速度 %d RPM\r\n", 
                      motor_x_direction ? "逆时针" : "顺时针", motor_x_speed);
            my_printf(&huart1, "  Y轴: %s, 速度 %d RPM\r\n", 
                      motor_y_direction ? "逆时针" : "顺时针", motor_y_speed);
            
            my_printf(&huart1, "==================\r\n\r\n");
        }
        else
        {
            my_printf(&huart1, "等待电机就绪... X轴:%s, Y轴:%s\r\n", 
                      motor01_ready ? "就绪" : "未就绪",
                      motor02_ready ? "就绪" : "未就绪");
        }
        
        last_test_time = current_time;
    }
}
```

### 2. 电机控制测试

```c
void Test_Motor_Control_With_Pulse_Feedback(void)
{
    static uint8_t test_step = 0;
    static uint32_t step_start_time = 0;
    uint32_t current_time = HAL_GetTick();
    
    // 确保电机就绪
    if (!motor01_ready || !motor02_ready)
        return;
    
    switch(test_step)
    {
        case 0: // 开始测试
            my_printf(&huart1, "开始电机控制测试...\r\n");
            StepMotor_Reset_Position();  // 重置脉冲计数器
            test_step = 1;
            step_start_time = current_time;
            break;
            
        case 1: // X轴正向移动1000脉冲
            if (current_time - step_start_time >= 1000)
            {
                my_printf(&huart1, "测试1: X轴正向移动1000脉冲\r\n");
                StepMotor_Move_Pulses(1000, 0);
                test_step = 2;
                step_start_time = current_time;
            }
            break;
            
        case 2: // 等待移动完成并检查结果
            if (current_time - step_start_time >= 3000)
            {
                StepMotor_Request_Position();  // 请求最新位置
                HAL_Delay(100);
                my_printf(&huart1, "移动完成，X轴脉冲数: %ld (期望: 1000)\r\n", motor_x_position);
                test_step = 3;
                step_start_time = current_time;
            }
            break;
            
        case 3: // Y轴负向移动500脉冲
            if (current_time - step_start_time >= 1000)
            {
                my_printf(&huart1, "测试2: Y轴负向移动500脉冲\r\n");
                StepMotor_Move_Pulses(0, -500);
                test_step = 4;
                step_start_time = current_time;
            }
            break;
            
        case 4: // 检查最终结果
            if (current_time - step_start_time >= 3000)
            {
                StepMotor_Request_Position();  // 请求最新位置
                HAL_Delay(100);
                my_printf(&huart1, "测试完成！最终位置:\r\n");
                my_printf(&huart1, "  X轴: %ld 脉冲 (期望: 1000)\r\n", motor_x_position);
                my_printf(&huart1, "  Y轴: %ld 脉冲 (期望: -500)\r\n", motor_y_position);
                test_step = 5; // 测试完成
            }
            break;
            
        case 5: // 测试完成，等待重新开始
            if (current_time - step_start_time >= 10000) // 10秒后重新开始
            {
                test_step = 0;
            }
            break;
    }
}
```

### 3. 在您的主循环中集成测试

```c
// 在您的主循环或定时任务中添加
void Your_Main_Loop(void)
{
    while(1)
    {
        // 您现有的任务调度
        Scheduler_Run();
        
        // 添加脉冲监控测试 (可选)
        Test_Pulse_Monitoring();
        
        // 添加电机控制测试 (可选，用于验证功能)
        // Test_Motor_Control_With_Pulse_Feedback();
    }
}
```

## 📊 预期输出示例

当您运行测试时，串口1应该输出类似这样的信息：

```
X轴电机就绪!
Y轴电机就绪!
位置计数器已重置

=== 脉冲监控测试 ===
当前脉冲数:
  X轴: 0 脉冲
  Y轴: 0 脉冲
编码器值:
  X轴: 0
  Y轴: 0
运动状态:
  X轴: 顺时针, 速度 0 RPM
  Y轴: 顺时针, 速度 0 RPM
==================

开始电机控制测试...
测试1: X轴正向移动1000脉冲
X轴: 位置=1000, 编码器=1000, 方向=0, 速度=50
移动完成，X轴脉冲数: 1000 (期望: 1000)
测试2: Y轴负向移动500脉冲
Y轴: 位置=-500, 编码器=500, 方向=1, 速度=30
测试完成！最终位置:
  X轴: 1000 脉冲 (期望: 1000)
  Y轴: -500 脉冲 (期望: -500)
```

## 🔧 调试建议

1. **首先测试基本功能**：
   - 确保能看到"X轴电机就绪!"和"Y轴电机就绪!"消息
   - 检查脉冲计数器是否从0开始

2. **测试数据接收**：
   - 手动移动电机，观察脉冲数是否变化
   - 检查方向标志是否正确

3. **测试主动请求**：
   - 调用`StepMotor_Request_Position()`
   - 观察是否收到位置更新

4. **验证控制功能**：
   - 使用`StepMotor_Move_Pulses()`移动电机
   - 检查实际移动的脉冲数是否与期望一致

## ✅ 系统状态

- ✅ 编译错误已修复
- ✅ 使用您的my_printf函数
- ✅ 脉冲计数功能已集成
- ✅ 自动数据解析已启用
- ✅ 调试输出已配置

**现在您可以开始测试和使用步进电机脉冲计数功能了！** 🚀
